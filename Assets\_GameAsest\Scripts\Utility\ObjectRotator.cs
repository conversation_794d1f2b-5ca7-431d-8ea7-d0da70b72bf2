using Unity.Netcode;
using Unity.Netcode.Components;
using UnityEngine;
using System.Collections.Generic;

[RequireComponent(typeof(NetworkTransform))]
public class ObjectRotator : NetworkBehaviour
{
    [Header("Dönme Ayarları")]
    [SerializeField] private float rotationSpeed = 50f;
    [SerializeField] private Vector3 rotationAxis = Vector3.up;

    [Header("Yörünge Ayarları")]
    [SerializeField] private bool enableOrbit = false;
    [SerializeField] private Transform orbitCenter;
    [SerializeField] private float orbitSpeed = 30f;
    [SerializeField] private Vector3 orbitAxis = Vector3.up;

    // Platformda olan oyuncuları takip et (sadece server'da)
    private readonly HashSet<CharacterController> playersOnPlatform = new HashSet<CharacterController>();
    private Vector3 lastPosition;
    private Quaternion lastRotation;

    public override void OnNetworkSpawn()
    {
        // Client'larda bu script'i devre dışı bırak, sadece NetworkTransform çalışsın
        if (!IsServer)
        {
            enabled = false;
            return;
        }

        lastPosition = transform.position;
        lastRotation = transform.rotation;
    }

    private void FixedUpdate()
    {
        // Bu hareket kodları SADECE server'da çalışır.
        if (!IsServer) return;

        // 1. Platformu döndür
        HandleRotation();

        // 2. Oyuncuları hareket ettir
        MovePlayers();
    }

    private void HandleRotation()
    {
        transform.Rotate(rotationAxis.normalized, rotationSpeed * Time.fixedDeltaTime);

        if (enableOrbit && orbitCenter != null)
        {
            transform.RotateAround(orbitCenter.position, orbitAxis.normalized, orbitSpeed * Time.fixedDeltaTime);
        }
    }

    private void MovePlayers()
    {
        // Pozisyon ve rotasyon değişimini hesapla
        Vector3 positionDelta = transform.position - lastPosition;
        Quaternion rotationDelta = transform.rotation * Quaternion.Inverse(lastRotation);

        // Oyuncuyu platforma "yapıştırmak" için aşağı yönlü küçük bir kuvvet
        Vector3 stickyForce = Vector3.down * 2f;

        foreach (CharacterController player in playersOnPlatform)
        {
            if (player != null)
            {
                // Oyuncunun platform merkezine göre pozisyonunu hesapla
                Vector3 relativePosition = player.transform.position - transform.position;

                // Rotasyon delta'sını uygula
                Vector3 rotatedPosition = rotationDelta * relativePosition;
                Vector3 finalMovement = (rotatedPosition - relativePosition) + positionDelta;

                // Hareket ve yapıştırma kuvvetini uygula
                player.Move(finalMovement + (stickyForce * Time.fixedDeltaTime));
            }
        }

        lastPosition = transform.position;
        lastRotation = transform.rotation;
    }

    private void OnTriggerEnter(Collider other)
    {
        if (!IsServer) return;

        // Oyuncunun ana CharacterController'ını bul ve listeye ekle
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController != null && playersOnPlatform.Add(playerController))
        {
            Debug.Log($"[ObjectRotator] Oyuncu platforma girdi: {playerController.name}");
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (!IsServer) return;

        // Oyuncuyu listeden çıkar
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController != null && playersOnPlatform.Remove(playerController))
        {
            Debug.Log($"[ObjectRotator] Oyuncu platformdan ayrıldı: {playerController.name}");
        }
    }

    private void OnDrawGizmosSelected()
    {
        if (enableOrbit && orbitCenter != null)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(orbitCenter.position, 0.5f);
            Gizmos.DrawLine(transform.position, orbitCenter.position);
        }
    }
}