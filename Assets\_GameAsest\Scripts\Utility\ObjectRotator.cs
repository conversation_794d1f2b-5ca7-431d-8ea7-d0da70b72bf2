using Unity.Netcode;
using Unity.Netcode.Components;
using UnityEngine;

[RequireComponent(typeof(NetworkTransform))]
public class ObjectRotator : NetworkBehaviour
{
    [Header("Dönme Ayarları")]
    [SerializeField] private float rotationSpeed = 50f;
    [SerializeField] private Vector3 rotationAxis = Vector3.up;

    [Header("Yörünge Ayarları")]
    [SerializeField] private bool enableOrbit = false;
    [SerializeField] private Transform orbitCenter;
    [SerializeField] private float orbitSpeed = 30f;
    [SerializeField] private Vector3 orbitAxis = Vector3.up;

    private void FixedUpdate()
    {
        // Bu hareket kodları SADECE server'da çalışır.
        if (!IsServer) return;

        transform.Rotate(rotationAxis.normalized, rotationSpeed * Time.fixedDeltaTime);

        if (enableOrbit && orbitCenter != null)
        {
            transform.RotateAround(orbitCenter.position, orbitAxis.normalized, orbitSpeed * Time.fixedDeltaTime);
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        // Parenting işlemini SADECE server yapabilir.
        if (!IsServer) return;

        // Çarpan objenin en kökündeki NetworkObject'i bul.
        NetworkObject playerNetworkObject = other.GetComponentInParent<NetworkObject>();

        // Eğer bu bir oyuncuysa ve zaten bu platformun çocuğu değilse, parent yap.
        if (playerNetworkObject != null && playerNetworkObject.CompareTag("Player"))
        {
            // Server'da parent yap
            playerNetworkObject.transform.SetParent(transform, true);
            
            // Client'lara da parent yapmasını söyle
            SetPlayerParentClientRpc(playerNetworkObject.NetworkObjectId, NetworkObjectId, true);
            
            Debug.Log($"[ObjectRotator] Oyuncu parent yapıldı: {playerNetworkObject.name}");
        }
    }

    private void OnTriggerExit(Collider other)
    {
        // Parenting işlemini SADECE server yapabilir.
        if (!IsServer) return;

        NetworkObject playerNetworkObject = other.GetComponentInParent<NetworkObject>();

        // Eğer bu bir oyuncuysa ve şu anki parent'ı bu platformsa, parent'tan ayır.
        if (playerNetworkObject != null && playerNetworkObject.CompareTag("Player") && playerNetworkObject.transform.parent == transform)
        {
            // Server'da parent'ı kaldır
            playerNetworkObject.transform.SetParent(null, true);
            
            // Client'lara da parent'ı kaldırmasını söyle
            SetPlayerParentClientRpc(playerNetworkObject.NetworkObjectId, NetworkObjectId, false);
            
            Debug.Log($"[ObjectRotator] Oyuncunun parent'ı kaldırıldı: {playerNetworkObject.name}");
        }
    }

    [ClientRpc]
    private void SetPlayerParentClientRpc(ulong playerNetworkObjectId, ulong platformNetworkObjectId, bool setParent)
    {
        // Client'ta çalışacak kod
        if (IsServer) return; // Server'da tekrar çalışmasın

        // Player NetworkObject'ini bul
        if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(playerNetworkObjectId, out NetworkObject playerNetworkObject))
        {
            if (setParent)
            {
                // Platform NetworkObject'ini bul ve parent yap
                if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(platformNetworkObjectId, out NetworkObject platformNetworkObject))
                {
                    playerNetworkObject.transform.SetParent(platformNetworkObject.transform, true);
                    Debug.Log($"[ObjectRotator CLIENT] Oyuncu parent yapıldı: {playerNetworkObject.name}");
                }
            }
            else
            {
                // Parent'ı kaldır
                playerNetworkObject.transform.SetParent(null, true);
                Debug.Log($"[ObjectRotator CLIENT] Oyuncunun parent'ı kaldırıldı: {playerNetworkObject.name}");
            }
        }
    }

    private void OnDrawGizmosSelected()
    {
        if (enableOrbit && orbitCenter != null)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(orbitCenter.position, 0.5f);
            Gizmos.DrawLine(transform.position, orbitCenter.position);
        }
    }
}