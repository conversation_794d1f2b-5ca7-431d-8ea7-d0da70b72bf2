using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

public class MovingPlatform : NetworkBehaviour
{
    [Header("Platform Ayarları")]
    [SerializeField] private float moveSpeed = 2f;
    [SerializeField] private float waitTime = 1f;

    [Header("Hareket Noktaları")]
    [SerializeField] private Transform[] waypoints;
    [SerializeField] private bool backAndForth = true;

    [Header("Görselleştirme")]
    [SerializeField] private bool showGizmos = true;

    // --- Private Değişkenler ---
    private Vector3 lastPosition;
    private int currentWaypointIndex = 0;
    private float waitTimer;
    private bool movingForward = true;

    // Sadece server'da tutulacak oyuncu listesi
    private readonly HashSet<CharacterController> playersOnPlatform = new HashSet<CharacterController>();

    // --- Unity & Network Fonksiyonları ---

    public override void OnNetworkSpawn()
    {
        // Bu script client'larda fiziksel bir işlem yapmayacak.
        // Sadece NetworkTransform pozisyonu güncelleyecek.
        if (!IsServer)
        {
            enabled = false;
            return;
        }

        // Server başlangıç pozisyonunu ayarlar
        if (waypoints.Length > 0)
        {
            transform.position = waypoints[0].position;
        }
        lastPosition = transform.position;
    }

    private void FixedUpdate()
    {
        // Bu kod sadece SERVER'da çalışır çünkü client'larda 'enabled = false' yaptık.

        // 1. Platformu Hareket Ettir
        HandlePlatformMovement();

        // 2. Oyuncuları Hareket Ettir
        MovePlayers();
    }

    private void HandlePlatformMovement()
    {
        if (waypoints.Length < 2) return;

        Vector3 targetPosition = waypoints[currentWaypointIndex].position;
        float distance = Vector3.Distance(transform.position, targetPosition);

        if (distance < 0.1f)
        {
            waitTimer += Time.fixedDeltaTime;
            if (waitTimer >= waitTime)
            {
                waitTimer = 0f;
                UpdateWaypointIndex();
            }
        }
        else
        {
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, moveSpeed * Time.fixedDeltaTime);
        }
    }

    private void MovePlayers()
    {
        Vector3 movementDelta = transform.position - lastPosition;

        // Oyuncuyu platforma "yapıştırmak" için aşağı yönlü küçük bir kuvvet.
        // Bu, CharacterController.isGrounded'ın true kalmasına yardımcı olur ve animasyonu düzeltir.
        Vector3 stickyForce = Vector3.down * 2f;

        foreach (CharacterController player in playersOnPlatform)
        {
            if (player != null)
            {
                // Platformun hareketini ve yapıştırma kuvvetini aynı anda uygula.
                player.Move(movementDelta + (stickyForce * Time.fixedDeltaTime));
            }
        }

        lastPosition = transform.position;
    }

    private void UpdateWaypointIndex()
    {
        if (backAndForth)
        {
            if (movingForward)
            {
                if (++currentWaypointIndex >= waypoints.Length)
                {
                    currentWaypointIndex = waypoints.Length - 2;
                    movingForward = false;
                }
            }
            else
            {
                if (--currentWaypointIndex < 0)
                {
                    currentWaypointIndex = 1;
                    movingForward = true;
                }
            }
        }
        else // Döngü
        {
            currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
        }
    }

    // --- Trigger Mantığı (Sadece Server'da Çalışır) ---

    private void OnTriggerEnter(Collider other)
    {
        if (!IsServer) return;

        // Oyuncunun ana CharacterController'ını bul ve listeye ekle
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController != null && playersOnPlatform.Add(playerController))
        {
            Debug.Log($"[MovingPlatform] Oyuncu platforma girdi: {playerController.name}");
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (!IsServer) return;

        // Oyuncuyu listeden çıkar
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController != null && playersOnPlatform.Remove(playerController))
        {
            Debug.Log($"[MovingPlatform] Oyuncu platformdan ayrıldı: {playerController.name}");
        }
    }

    // --- Diğer Fonksiyonlar ---

    private void OnDrawGizmosSelected()
    {
        if (!showGizmos || waypoints == null || waypoints.Length < 2) return;

        for (int i = 0; i < waypoints.Length; i++)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(waypoints[i].position, 0.3f);

            if (i < waypoints.Length - 1)
                Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
        }

        if (!backAndForth && waypoints.Length > 1)
            Gizmos.DrawLine(waypoints[waypoints.Length - 1].position, waypoints[0].position);
    }

    private void CreateDefaultWaypoints()
    {
        waypoints = new Transform[2];
        GameObject p1 = new GameObject("Waypoint 1");
        GameObject p2 = new GameObject("Waypoint 2");
        p1.transform.SetParent(transform.parent);
        p2.transform.SetParent(transform.parent);
        p1.transform.position = transform.position;
        p2.transform.position = transform.position + Vector3.right * 5f;
        waypoints[0] = p1.transform;
        waypoints[1] = p2.transform;
    }
}